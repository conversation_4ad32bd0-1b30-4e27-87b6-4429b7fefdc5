# NVH数据管理系统

基于Django + Vue.js + MySQL + Keycloak的NVH数据管理系统。

## 技术栈

### 后端
- **Django 4.2.7** - Web框架
- **Django REST Framework** - API框架
- **MySQL 8** - 数据库
- **Keycloak** - 身份认证和授权

### 前端
- **Vue 3** - 前端框架
- **Vite** - 构建工具
- **Element Plus** - UI组件库
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP客户端

## 项目结构

```
nvh_django/
├── backend/                    # Django后端
│   ├── manage.py
│   ├── requirements.txt
│   ├── nvh_backend/           # Django主配置
│   ├── apps/                  # 功能模块
│   │   └── users/             # 用户管理
│   ├── middleware/            # 自定义中间件
│   │   └── keycloak_middleware.py
│   └── utils/                 # 工具函数
│       └── keycloak_utils.py
├── frontend/                  # Vue前端
│   ├── package.json
│   ├── vite.config.js
│   └── src/
│       ├── main.js
│       ├── App.vue
│       ├── views/
│       ├── router/
│       ├── store/
│       ├── api/
│       └── utils/
└── docs/                      # 项目文档
```

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- MySQL 8.0+

### 后端设置

1. 创建虚拟环境并激活
```bash
cd backend
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量
创建 `.env` 文件：
```env
SECRET_KEY=your-secret-key-here
DEBUG=True
DB_NAME=nvh_database
DB_USER=root
DB_PASSWORD=your-password
DB_HOST=localhost
DB_PORT=3306
```

4. 初始化数据库
```bash
# 方法1: 使用自动化脚本（推荐）
python init_db.py

# 方法2: 手动执行
python manage.py makemigrations
python manage.py migrate
```

5. 创建超级用户（可选）
```bash
python manage.py createsuperuser
```

6. 启动开发服务器
```bash
python manage.py runserver
```

### 前端设置

1. 安装依赖
```bash
cd frontend
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

## Keycloak配置

### 测试环境配置
- **服务器地址**: `https://account-test.sgmw.com.cn/auth/`
- **Realm**: `demo`
- **前端客户端ID**: `front` (public)
- **后端客户端ID**: `backend` (bearer-only)
- **后端客户端密钥**: `8545c061-7cf7-41e5-b92b-e6769a6a75b8`

### 测试账号
- **用户名**: `test`
- **密码**: `B5FDs0PcyuTipj^！`

## API接口

### 认证相关
- `GET /api/auth/health/` - 健康检查
- `GET /api/users/info/` - 获取用户信息
- `GET /api/users/profile/` - 获取用户资料

## 开发说明

### 认证流程
1. 前端使用Keycloak JS适配器进行用户认证
2. 获取JWT token后，在每个API请求中携带Authorization头
3. 后端中间件验证token并获取用户信息
4. 自动创建或更新Django用户记录

### 中间件说明
- `KeycloakMiddleware`: 处理Keycloak token验证和用户信息同步

### 前端路由守卫
- 所有需要认证的路由都会检查Keycloak认证状态
- 未认证用户会自动跳转到Keycloak登录页面

## 部署

### 生产环境配置
1. 设置正确的环境变量
2. 配置生产数据库
3. 设置静态文件服务
4. 配置HTTPS
5. 更新Keycloak客户端配置

### Docker部署（待实现）
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

## 常见问题

### MySQL连接问题

**问题**: `ModuleNotFoundError: No module named 'MySQLdb'`

**解决方案**:
项目已配置使用PyMySQL作为MySQL驱动，确保：
1. 已安装PyMySQL: `pip install PyMySQL==1.1.0`
2. MySQL服务正在运行
3. 数据库配置正确（检查.env文件）

**问题**: `Access denied for user 'root'@'localhost'`

**解决方案**:
1. 检查MySQL用户名和密码
2. 确保用户有创建数据库的权限
3. 可以使用 `python backend/init_db.py` 测试连接

### Keycloak认证问题

**问题**: CSP违规和iframe超时错误
```
Refused to frame 'https://account-test.sgmw.com.cn/' because an ancestor violates the following Content Security Policy directive
Keycloak initialization failed {error: 'Timeout when waiting for 3rd party check iframe message.'}
```

**解决方案**:
项目已配置开发环境专用的Keycloak设置来绕过CSP限制：
1. 开发环境自动使用 `keycloak-dev.js` 配置
2. 禁用iframe检查：`checkLoginIframe: false`
3. 使用标准授权码流程而不是iframe
4. 可以访问 `/auth-test` 页面测试认证状态

**问题**: 前端无法连接Keycloak服务器

**解决方案**:
1. 检查网络连接
2. 确认Keycloak服务器地址正确
3. 检查客户端ID配置
4. 使用认证测试页面：`http://localhost:5173/auth-test`

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
