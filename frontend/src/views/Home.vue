<template>
  <div class="home">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>NVH数据管理系统</h1>
          <div class="user-info">
            <span>欢迎, {{ userStore.fullName || userStore.username }}</span>
            <el-button @click="logout" type="primary" size="small">退出登录</el-button>
          </div>
        </div>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>系统状态</span>
                </div>
              </template>
              <div v-if="loading">
                <el-skeleton :rows="3" animated />
              </div>
              <div v-else>
                <p><strong>系统状态:</strong> <el-tag type="success">正常运行</el-tag></p>
                <p><strong>当前用户:</strong> {{ userStore.username }}</p>
                <p><strong>邮箱:</strong> {{ userStore.email }}</p>
                <p><strong>认证状态:</strong> <el-tag type="success">已认证</el-tag></p>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>快速操作</span>
                </div>
              </template>
              <el-button-group>
                <el-button @click="$router.push('/profile')" type="primary">查看个人信息</el-button>
                <el-button @click="refreshUserInfo" type="success">刷新用户信息</el-button>
              </el-button-group>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>系统信息</span>
                </div>
              </template>
              <p>前端框架: Vue 3 + Vite</p>
              <p>后端框架: Django + DRF</p>
              <p>认证系统: Keycloak</p>
              <p>UI组件: Element Plus</p>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store'
import { userApi } from '@/api/user'
import keycloak from '@/utils/keycloak'

const userStore = useUserStore()
const loading = ref(false)

const logout = () => {
  keycloak.logout()
}

const refreshUserInfo = async () => {
  try {
    loading.value = true
    const userInfo = await userApi.getUserInfo()
    userStore.setUserInfo(userInfo)
    ElMessage.success('用户信息刷新成功')
  } catch (error) {
    console.error('Failed to refresh user info:', error)
    ElMessage.error('刷新用户信息失败')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  try {
    loading.value = true
    const userInfo = await userApi.getUserInfo()
    userStore.setUserInfo(userInfo)
  } catch (error) {
    console.error('Failed to load user info:', error)
    ElMessage.error('加载用户信息失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.home {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-content h1 {
  margin: 0;
  color: #409eff;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-main {
  padding: 20px;
}
</style>
