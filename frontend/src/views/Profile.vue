<template>
  <div class="profile">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>个人信息</h1>
          <el-button @click="$router.push('/')" type="primary">返回首页</el-button>
        </div>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>用户详细信息</span>
                  <el-button @click="loadUserInfo" type="primary" size="small">刷新</el-button>
                </div>
              </template>
              
              <div v-if="loading">
                <el-skeleton :rows="5" animated />
              </div>
              
              <div v-else-if="userStore.userInfo">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="用户名">
                    {{ userStore.userInfo.user.username }}
                  </el-descriptions-item>
                  <el-descriptions-item label="邮箱">
                    {{ userStore.userInfo.user.email }}
                  </el-descriptions-item>
                  <el-descriptions-item label="姓">
                    {{ userStore.userInfo.user.first_name || '未设置' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="名">
                    {{ userStore.userInfo.user.last_name || '未设置' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="账户状态">
                    <el-tag :type="userStore.userInfo.user.is_active ? 'success' : 'danger'">
                      {{ userStore.userInfo.user.is_active ? '激活' : '未激活' }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="注册时间">
                    {{ formatDate(userStore.userInfo.user.date_joined) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="Keycloak ID">
                    {{ userStore.userInfo.profile.keycloak_id || '未设置' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="部门">
                    {{ userStore.userInfo.profile.department || '未设置' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              
              <div v-else>
                <el-empty description="无法加载用户信息" />
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;" v-if="userStore.userInfo">
          <el-col :span="24">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>Keycloak 信息</span>
                </div>
              </template>
              
              <el-descriptions :column="2" border>
                <el-descriptions-item label="Subject">
                  {{ userStore.userInfo.keycloak_info.sub }}
                </el-descriptions-item>
                <el-descriptions-item label="Preferred Username">
                  {{ userStore.userInfo.keycloak_info.preferred_username }}
                </el-descriptions-item>
                <el-descriptions-item label="Email Verified">
                  <el-tag :type="userStore.userInfo.keycloak_info.email_verified ? 'success' : 'warning'">
                    {{ userStore.userInfo.keycloak_info.email_verified ? '已验证' : '未验证' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="Given Name">
                  {{ userStore.userInfo.keycloak_info.given_name || '未设置' }}
                </el-descriptions-item>
                <el-descriptions-item label="Family Name">
                  {{ userStore.userInfo.keycloak_info.family_name || '未设置' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store'
import { userApi } from '@/api/user'

const userStore = useUserStore()
const loading = ref(false)

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

const loadUserInfo = async () => {
  try {
    loading.value = true
    const userInfo = await userApi.getUserInfo()
    userStore.setUserInfo(userInfo)
    ElMessage.success('用户信息加载成功')
  } catch (error) {
    console.error('Failed to load user info:', error)
    ElMessage.error('加载用户信息失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  if (!userStore.userInfo) {
    loadUserInfo()
  }
})
</script>

<style scoped>
.profile {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-content h1 {
  margin: 0;
  color: #409eff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-main {
  padding: 20px;
}
</style>
