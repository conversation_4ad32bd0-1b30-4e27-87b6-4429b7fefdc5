<template>
  <div class="auth-test">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>Keycloak认证测试</h1>
          <div class="auth-status">
            <el-tag :type="authStatus.type">{{ authStatus.text }}</el-tag>
          </div>
        </div>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>认证信息</span>
                  <el-button @click="refreshAuth" type="primary" size="small">刷新</el-button>
                </div>
              </template>
              
              <div v-if="loading">
                <el-skeleton :rows="3" animated />
              </div>
              
              <div v-else>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="认证状态">
                    <el-tag :type="keycloakInfo.authenticated ? 'success' : 'danger'">
                      {{ keycloakInfo.authenticated ? '已认证' : '未认证' }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="Token存在">
                    <el-tag :type="keycloakInfo.token ? 'success' : 'danger'">
                      {{ keycloakInfo.token ? '是' : '否' }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="用户名">
                    {{ keycloakInfo.username || '未获取' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="邮箱">
                    {{ keycloakInfo.email || '未获取' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="Token过期时间">
                    {{ keycloakInfo.tokenExpiry || '未知' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="刷新Token过期时间">
                    {{ keycloakInfo.refreshTokenExpiry || '未知' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>操作</span>
                </div>
              </template>
              <el-button-group>
                <el-button @click="testLogin" type="primary">测试登录</el-button>
                <el-button @click="testLogout" type="danger">测试登出</el-button>
                <el-button @click="testRefreshToken" type="success">刷新Token</el-button>
              </el-button-group>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>API测试</span>
                </div>
              </template>
              <el-button-group>
                <el-button @click="testHealthAPI" type="info">健康检查</el-button>
                <el-button @click="testUserAPI" type="warning">用户信息</el-button>
              </el-button-group>
              <div style="margin-top: 10px;">
                <el-button-group>
                  <el-button @click="testKeycloakAPI" type="success">Keycloak测试</el-button>
                  <el-button @click="testMiddlewareAPI" type="primary">中间件测试</el-button>
                </el-button-group>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;" v-if="apiResult">
          <el-col :span="24">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>API响应</span>
                </div>
              </template>
              <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { userApi } from '@/api/user'

const loading = ref(false)
const keycloakInfo = ref({})
const apiResult = ref(null)

// 动态导入Keycloak实例
let keycloak = null
const isDev = import.meta.env.DEV

const initKeycloakInstance = async () => {
  if (!keycloak) {
    if (isDev) {
      const { default: keycloakDev } = await import('@/utils/keycloak-dev.js')
      keycloak = keycloakDev
    } else {
      const { default: keycloakProd } = await import('@/utils/keycloak.js')
      keycloak = keycloakProd
    }
  }
  return keycloak
}

const authStatus = computed(() => {
  if (keycloakInfo.value.authenticated) {
    return { type: 'success', text: '认证成功' }
  } else {
    return { type: 'danger', text: '未认证' }
  }
})

const refreshAuth = async () => {
  try {
    loading.value = true
    const keycloakInstance = await initKeycloakInstance()
    
    keycloakInfo.value = {
      authenticated: keycloakInstance.authenticated,
      token: !!keycloakInstance.token,
      username: keycloakInstance.tokenParsed?.preferred_username,
      email: keycloakInstance.tokenParsed?.email,
      tokenExpiry: keycloakInstance.tokenParsed?.exp 
        ? new Date(keycloakInstance.tokenParsed.exp * 1000).toLocaleString()
        : null,
      refreshTokenExpiry: keycloakInstance.refreshTokenParsed?.exp
        ? new Date(keycloakInstance.refreshTokenParsed.exp * 1000).toLocaleString()
        : null
    }
  } catch (error) {
    console.error('Failed to refresh auth info:', error)
    ElMessage.error('刷新认证信息失败')
  } finally {
    loading.value = false
  }
}

const testLogin = async () => {
  try {
    const keycloakInstance = await initKeycloakInstance()
    keycloakInstance.login()
  } catch (error) {
    console.error('Login test failed:', error)
    ElMessage.error('登录测试失败')
  }
}

const testLogout = async () => {
  try {
    const keycloakInstance = await initKeycloakInstance()
    keycloakInstance.logout()
  } catch (error) {
    console.error('Logout test failed:', error)
    ElMessage.error('登出测试失败')
  }
}

const testRefreshToken = async () => {
  try {
    const keycloakInstance = await initKeycloakInstance()
    const refreshed = await keycloakInstance.updateToken(30)
    if (refreshed) {
      ElMessage.success('Token刷新成功')
      refreshAuth()
    } else {
      ElMessage.info('Token仍然有效，无需刷新')
    }
  } catch (error) {
    console.error('Token refresh failed:', error)
    ElMessage.error('Token刷新失败')
  }
}

const testHealthAPI = async () => {
  try {
    const result = await userApi.healthCheck()
    apiResult.value = result
    ElMessage.success('健康检查API调用成功')
  } catch (error) {
    console.error('Health API test failed:', error)
    apiResult.value = { error: error.message }
    ElMessage.error('健康检查API调用失败')
  }
}

const testUserAPI = async () => {
  try {
    const result = await userApi.getUserInfo()
    apiResult.value = result
    ElMessage.success('用户信息API调用成功')
  } catch (error) {
    console.error('User API test failed:', error)
    apiResult.value = { error: error.message }
    ElMessage.error('用户信息API调用失败')
  }
}

const testKeycloakAPI = async () => {
  try {
    const result = await userApi.keycloakTest()
    apiResult.value = result
    ElMessage.success('Keycloak测试API调用成功')
  } catch (error) {
    console.error('Keycloak API test failed:', error)
    apiResult.value = { error: error.message }
    ElMessage.error('Keycloak测试API调用失败')
  }
}

const testMiddlewareAPI = async () => {
  try {
    const result = await userApi.middlewareTest()
    apiResult.value = result
    ElMessage.success('中间件测试API调用成功')
  } catch (error) {
    console.error('Middleware API test failed:', error)
    apiResult.value = { error: error.message }
    ElMessage.error('中间件测试API调用失败')
  }
}

onMounted(() => {
  refreshAuth()
})
</script>

<style scoped>
.auth-test {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-content h1 {
  margin: 0;
  color: #409eff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-main {
  padding: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
