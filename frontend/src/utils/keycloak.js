import Keycloak from 'keycloak-js'

// Keycloak configuration
const initOptions = {
  url: 'https://account-test.sgmw.com.cn/auth/',
  realm: 'demo',
  clientId: 'front',
  onLoad: 'login-required'
}

// Create Keycloak instance
const keycloak = new Keycloak(initOptions)

// Initialize Keycloak
export const initKeycloak = () => {
  return new Promise((resolve, reject) => {
    keycloak.init({ 
      onLoad: initOptions.onLoad, 
      checkLoginIframe: false,
      silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html'
    }).then((authenticated) => {
      if (!authenticated) {
        console.log('User not authenticated')
        window.location.reload()
      } else {
        console.log('User authenticated successfully')
        
        // Set up token refresh
        setInterval(() => {
          keycloak.updateToken(70).then((refreshed) => {
            if (refreshed) {
              console.log('Token refreshed')
            } else {
              console.log('Token not refreshed, valid for ' 
                + Math.round(keycloak.tokenParsed.exp + keycloak.timeSkew - new Date().getTime() / 1000) + ' seconds')
            }
          }).catch(error => {
            console.error('Failed to refresh token', error)
          })
        }, 60000)
      }
      
      resolve(keycloak)
    }).catch(error => {
      console.error('Keycloak initialization failed', error)
      reject(error)
    })
  })
}

export default keycloak
