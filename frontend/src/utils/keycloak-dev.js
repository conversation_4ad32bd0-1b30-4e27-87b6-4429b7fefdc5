import Keycloak from 'keycloak-js'

// 开发环境专用的Keycloak配置
const initOptions = {
  url: import.meta.env.VITE_KEYCLOAK_URL || 'https://account-test.sgmw.com.cn/auth/',
  realm: import.meta.env.VITE_KEYCLOAK_REALM || 'demo',
  clientId: import.meta.env.VITE_KEYCLOAK_CLIENT_ID || 'front',
  onLoad: 'login-required'
}

// Create Keycloak instance
const keycloak = new Keycloak(initOptions)

// 开发环境初始化Keycloak（绕过CSP限制）
export const initKeycloak = () => {
  return new Promise((resolve, reject) => {
    console.log('正在初始化Keycloak认证...')
    
    keycloak.init({ 
      onLoad: initOptions.onLoad,
      // 完全禁用iframe相关功能
      checkLoginIframe: false,
      checkLoginIframeInterval: 0,
      // 启用日志以便调试
      enableLogging: true,
      // 使用标准授权码流程
      flow: 'standard',
      // 设置响应模式为query（避免fragment问题）
      responseMode: 'query',
      // 禁用静默检查
      silentCheckSsoRedirectUri: null,
      // 增加超时时间
      messageReceiveTimeout: 30000,
      // PKCE支持
      pkceMethod: 'S256'
    }).then((authenticated) => {
      if (!authenticated) {
        console.log('用户未认证，跳转到登录页面')
        // 直接跳转登录，不使用iframe
        keycloak.login({
          redirectUri: window.location.origin
        })
      } else {
        console.log('用户认证成功')
        console.log('Token:', keycloak.token)
        console.log('用户信息:', keycloak.tokenParsed)
        
        // 设置token刷新
        setupTokenRefresh()
      }
      
      resolve(keycloak)
    }).catch(error => {
      console.error('Keycloak初始化失败:', error)
      
      // 如果是CSP或iframe相关错误，尝试直接登录
      if (error.error && (
        error.error.includes('frame') || 
        error.error.includes('iframe') ||
        error.error.includes('Timeout')
      )) {
        console.log('检测到CSP/iframe问题，尝试直接登录')
        keycloak.login({
          redirectUri: window.location.origin
        })
      } else {
        reject(error)
      }
    })
  })
}

// 设置token刷新机制
const setupTokenRefresh = () => {
  // 每60秒检查token是否需要刷新
  setInterval(() => {
    keycloak.updateToken(70).then((refreshed) => {
      if (refreshed) {
        console.log('Token已刷新')
      } else {
        console.log('Token仍然有效，剩余时间: ' 
          + Math.round(keycloak.tokenParsed.exp + keycloak.timeSkew - new Date().getTime() / 1000) + ' 秒')
      }
    }).catch(error => {
      console.error('Token刷新失败:', error)
      // 如果刷新失败，重新登录
      console.log('重新登录...')
      keycloak.login()
    })
  }, 60000)
}

// 导出keycloak实例
export default keycloak
