import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import { pinia } from './store'

// 根据环境选择不同的Keycloak配置
const isDev = import.meta.env.DEV
const { initKeycloak } = isDev
  ? await import('./utils/keycloak-dev.js')
  : await import('./utils/keycloak.js')

// Initialize Keycloak first, then create Vue app
initKeycloak().then((keycloak) => {
  const app = createApp(App)
  
  // Use plugins
  app.use(ElementPlus)
  app.use(router)
  app.use(pinia)
  
  // Make keycloak available globally
  app.config.globalProperties.$keycloak = keycloak
  
  // Mount app
  app.mount('#app')
  
  console.log('Vue app initialized with Keycloak authentication')
}).catch(error => {
  console.error('Failed to initialize application:', error)
})
