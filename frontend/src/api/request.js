import axios from 'axios'

// 动态导入Keycloak实例
let keycloak = null
const isDev = import.meta.env.DEV

// 异步初始化keycloak实例
const initKeycloakInstance = async () => {
  if (!keycloak) {
    if (isDev) {
      const { default: keycloakDev } = await import('@/utils/keycloak-dev.js')
      keycloak = keycloakDev
    } else {
      const { default: keycloakProd } = await import('@/utils/keycloak.js')
      keycloak = keycloakProd
    }
  }
  return keycloak
}

// Create axios instance
const service = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
service.interceptors.request.use(
  async config => {
    // 确保keycloak实例已初始化
    const keycloakInstance = await initKeycloakInstance()

    // Add Authorization header if user is authenticated
    if (keycloakInstance.authenticated && keycloakInstance.token) {
      config.headers['Authorization'] = 'Bearer ' + keycloakInstance.token
    }
    return config
  },
  error => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
service.interceptors.response.use(
  response => {
    return response.data
  },
  async error => {
    console.error('Response error:', error)

    // Handle 401 Unauthorized
    if (error.response && error.response.status === 401) {
      console.log('Token expired or invalid, redirecting to login')
      const keycloakInstance = await initKeycloakInstance()
      keycloakInstance.login()
      return Promise.reject(error)
    }

    // Handle other errors
    const message = error.response?.data?.message || error.message || 'Unknown error'
    return Promise.reject(new Error(message))
  }
)

export default service
