import request from './request'

// User API
export const userApi = {
  // Get user info
  getUserInfo() {
    return request({
      url: '/users/info/',
      method: 'get'
    })
  },
  
  // Get user profile
  getUserProfile() {
    return request({
      url: '/users/profile/',
      method: 'get'
    })
  },
  
  // Health check
  healthCheck() {
    return request({
      url: '/auth/health/',
      method: 'get'
    })
  },

  // Keycloak test
  keycloakTest() {
    return request({
      url: '/users/keycloak-test/',
      method: 'get'
    })
  },

  // Middleware test
  middlewareTest() {
    return request({
      url: '/auth/middleware-test/',
      method: 'get'
    })
  },

  // Debug token
  debugToken() {
    return request({
      url: '/auth/debug-token/',
      method: 'post'
    })
  }
}
