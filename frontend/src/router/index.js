import { createRouter, createWebHistory } from 'vue-router'

// 动态导入Keycloak实例
let keycloak = null
const isDev = import.meta.env.DEV

if (isDev) {
  const { default: keycloakDev } = await import('@/utils/keycloak-dev.js')
  keycloak = keycloakDev
} else {
  const { default: keycloakProd } = await import('@/utils/keycloak.js')
  keycloak = keycloakProd
}

// Import views
import Home from '@/views/Home.vue'
import Profile from '@/views/Profile.vue'
import AuthTest from '@/views/AuthTest.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/auth-test',
    name: 'AuthTest',
    component: AuthTest,
    meta: { requiresAuth: false } // 测试页面不需要认证
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard
router.beforeEach((to, from, next) => {
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (keycloak.authenticated) {
      next()
    } else {
      keycloak.login()
    }
  } else {
    next()
  }
})

export default router
