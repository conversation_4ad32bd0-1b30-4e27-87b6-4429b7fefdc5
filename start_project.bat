@echo off
echo ========================================
echo NVH数据管理系统启动脚本
echo ========================================

echo.
echo 1. 启动Django后端服务器...
cd backend
start cmd /k "python manage.py runserver"

echo.
echo 2. 等待3秒后启动Vue前端服务器...
timeout /t 3 /nobreak > nul

cd ../frontend
start cmd /k "npm run dev"

echo.
echo ========================================
echo 项目启动完成！
echo 后端地址: http://localhost:8000
echo 前端地址: http://localhost:5173
echo ========================================
echo.
echo 按任意键退出...
pause > nul
