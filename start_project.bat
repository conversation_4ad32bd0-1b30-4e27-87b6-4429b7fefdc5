@echo off
echo ========================================
echo NVH数据管理系统启动脚本
echo ========================================

echo.
echo 检查环境配置...

cd backend

REM 检查.env文件是否存在
if not exist .env (
    echo 警告: 未找到.env文件，请复制.env.example为.env并配置数据库信息
    echo.
    set /p continue="是否继续使用默认配置? (y/N): "
    if /i not "!continue!"=="y" (
        echo 已取消启动
        pause
        exit /b
    )
)

echo.
echo 1. 初始化数据库...
python init_db.py
if errorlevel 1 (
    echo 数据库初始化失败，请检查MySQL连接配置
    pause
    exit /b
)

echo.
echo 2. 启动Django后端服务器...
start cmd /k "python manage.py runserver"

echo.
echo 3. 等待3秒后启动Vue前端服务器...
timeout /t 3 /nobreak > nul

cd ../frontend

REM 检查node_modules是否存在
if not exist node_modules (
    echo 正在安装前端依赖...
    npm install
)

start cmd /k "npm run dev"

echo.
echo ========================================
echo 项目启动完成！
echo 后端地址: http://localhost:8000
echo 前端地址: http://localhost:5173
echo 测试账号: test / B5FDs0PcyuTipj^！
echo ========================================
echo.
echo 按任意键退出...
pause > nul
