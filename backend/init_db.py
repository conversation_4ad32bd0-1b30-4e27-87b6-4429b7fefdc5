#!/usr/bin/env python
"""
数据库初始化脚本
用于创建MySQL数据库和执行Django迁移
"""
import os
import sys
import pymysql
from decouple import config

def create_database():
    """创建MySQL数据库"""
    try:
        # 数据库连接配置
        db_config = {
            'host': config('DB_HOST', default='localhost'),
            'port': int(config('DB_PORT', default='3306')),
            'user': config('DB_USER', default='root'),
            'password': config('DB_PASSWORD', default='123456'),
            'charset': 'utf8mb4'
        }
        
        db_name = config('DB_NAME', default='nvh_database')
        
        print(f"正在连接MySQL服务器 {db_config['host']}:{db_config['port']}...")
        
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            result = cursor.fetchone()
            
            if result:
                print(f"数据库 '{db_name}' 已存在")
            else:
                # 创建数据库
                cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                print(f"数据库 '{db_name}' 创建成功")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"数据库操作失败: {e}")
        print("\n请检查:")
        print("1. MySQL服务是否正在运行")
        print("2. 数据库连接配置是否正确")
        print("3. 用户是否有创建数据库的权限")
        return False

def run_migrations():
    """执行Django数据库迁移"""
    try:
        print("\n正在执行Django数据库迁移...")
        
        # 设置Django设置模块
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nvh_backend.settings')
        
        # 导入Django管理命令
        from django.core.management import execute_from_command_line
        
        # 执行makemigrations
        print("1. 生成迁移文件...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # 执行migrate
        print("2. 应用迁移...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("数据库迁移完成!")
        return True
        
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("NVH数据管理系统 - 数据库初始化")
    print("=" * 50)
    
    # 检查是否存在.env文件
    env_file = '.env'
    if not os.path.exists(env_file):
        print(f"\n警告: 未找到 {env_file} 文件")
        print("请复制 .env.example 为 .env 并配置数据库连接信息")
        
        # 询问是否使用默认配置
        use_default = input("\n是否使用默认配置继续? (y/N): ").lower().strip()
        if use_default != 'y':
            print("已取消初始化")
            return
    
    # 创建数据库
    if create_database():
        # 执行迁移
        if run_migrations():
            print("\n" + "=" * 50)
            print("数据库初始化完成!")
            print("现在可以运行 'python manage.py runserver' 启动服务器")
            print("=" * 50)
        else:
            print("\n数据库迁移失败，请检查错误信息")
    else:
        print("\n数据库创建失败，请检查MySQL连接配置")

if __name__ == '__main__':
    main()
