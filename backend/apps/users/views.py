from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.contrib.auth.models import User
from .models import UserProfile
from .serializers import UserInfoSerializer, UserSerializer
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """Health check endpoint"""
    return Response({
        'status': 'healthy',
        'message': 'NVH Backend API is running'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    """Get current user information"""
    try:
        user = request.user
        
        # Get or create user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'keycloak_id': getattr(request, 'keycloak_user_info', {}).get('sub', '')
            }
        )
        
        # Prepare response data
        response_data = {
            'user': UserSerializer(user).data,
            'profile': {
                'keycloak_id': profile.keycloak_id,
                'department': profile.department,
                'phone': profile.phone,
                'created_at': profile.created_at,
                'updated_at': profile.updated_at,
            },
            'keycloak_info': getattr(request, 'keycloak_user_info', {})
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        return Response(
            {'error': 'Failed to get user information'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """Get current user profile"""
    try:
        user = request.user
        profile, created = UserProfile.objects.get_or_create(user=user)
        
        data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'department': profile.department,
            'phone': profile.phone,
            'is_active': user.is_active,
            'date_joined': user.date_joined,
        }
        
        return Response(data)
        
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return Response(
            {'error': 'Failed to get user profile'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def keycloak_test(request):
    """Keycloak集成测试端点"""
    try:
        user = request.user

        # 获取Keycloak相关信息
        keycloak_token_info = getattr(request, 'keycloak_token_info', {})
        keycloak_user_info = getattr(request, 'keycloak_user_info', {})

        response_data = {
            'message': 'Keycloak认证测试成功',
            'django_user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_authenticated': user.is_authenticated,
                'is_active': user.is_active,
            },
            'keycloak_token_info': {
                'active': keycloak_token_info.get('active', False),
                'client_id': keycloak_token_info.get('client_id', ''),
                'username': keycloak_token_info.get('username', ''),
                'exp': keycloak_token_info.get('exp', 0),
                'iat': keycloak_token_info.get('iat', 0),
            },
            'keycloak_user_info': {
                'sub': keycloak_user_info.get('sub', ''),
                'preferred_username': keycloak_user_info.get('preferred_username', ''),
                'email': keycloak_user_info.get('email', ''),
                'email_verified': keycloak_user_info.get('email_verified', False),
                'given_name': keycloak_user_info.get('given_name', ''),
                'family_name': keycloak_user_info.get('family_name', ''),
            },
            'middleware_info': {
                'has_keycloak_token_info': hasattr(request, 'keycloak_token_info'),
                'has_keycloak_user_info': hasattr(request, 'keycloak_user_info'),
            }
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"Keycloak test error: {e}")
        return Response(
            {'error': f'Keycloak测试失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def middleware_test(request):
    """中间件测试端点（无需认证）"""
    try:
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        has_bearer_token = auth_header.startswith('Bearer ')

        response_data = {
            'message': '中间件测试端点',
            'request_info': {
                'path': request.path,
                'method': request.method,
                'has_auth_header': bool(auth_header),
                'has_bearer_token': has_bearer_token,
                'user_authenticated': request.user.is_authenticated if hasattr(request, 'user') else False,
                'user_username': request.user.username if hasattr(request, 'user') and request.user.is_authenticated else None,
            },
            'middleware_info': {
                'has_keycloak_token_info': hasattr(request, 'keycloak_token_info'),
                'has_keycloak_user_info': hasattr(request, 'keycloak_user_info'),
            }
        }

        if has_bearer_token:
            token = auth_header.split(' ')[1]
            response_data['token_info'] = {
                'token_length': len(token),
                'token_preview': token[:20] + '...' if len(token) > 20 else token,
            }

        return Response(response_data)

    except Exception as e:
        logger.error(f"Middleware test error: {e}")
        return Response(
            {'error': f'中间件测试失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def debug_token(request):
    """调试Token验证过程"""
    try:
        # 获取token
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if not auth_header.startswith('Bearer '):
            return Response({
                'error': 'Missing or invalid Authorization header',
                'expected_format': 'Authorization: Bearer <token>',
                'received': auth_header[:50] + '...' if len(auth_header) > 50 else auth_header
            }, status=400)

        token = auth_header.split(' ')[1]

        # 导入Keycloak工具
        from utils.keycloak_utils import keycloak_utils

        # 测试步骤1: 检查端点
        endpoints = keycloak_utils._get_endpoints()
        if not endpoints:
            return Response({
                'error': 'Failed to get Keycloak endpoints',
                'keycloak_url': keycloak_utils.auth_server_url,
                'realm': keycloak_utils.realm
            }, status=500)

        # 测试步骤2: Token内省
        token_info = keycloak_utils.introspect_token(token)

        # 测试步骤3: 获取用户信息
        user_info = None
        if token_info:
            user_info = keycloak_utils.get_user_info(token)

        response_data = {
            'debug_info': {
                'token_length': len(token),
                'token_preview': token[:20] + '...' if len(token) > 20 else token,
                'endpoints_available': bool(endpoints),
                'introspection_endpoint': endpoints.get('introspection_endpoint') if endpoints else None,
                'userinfo_endpoint': endpoints.get('userinfo_endpoint') if endpoints else None,
            },
            'token_introspection': {
                'success': bool(token_info),
                'active': token_info.get('active') if token_info else False,
                'client_id': token_info.get('client_id') if token_info else None,
                'username': token_info.get('username') if token_info else None,
                'exp': token_info.get('exp') if token_info else None,
            },
            'user_info': {
                'success': bool(user_info),
                'preferred_username': user_info.get('preferred_username') if user_info else None,
                'email': user_info.get('email') if user_info else None,
            },
            'keycloak_config': {
                'realm': keycloak_utils.realm,
                'auth_server_url': keycloak_utils.auth_server_url,
                'client_id': keycloak_utils.client_id,
                'client_secret_set': bool(keycloak_utils.client_secret),
            }
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"Debug token error: {e}")
        return Response(
            {'error': f'调试失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
