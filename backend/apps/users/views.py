from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.contrib.auth.models import User
from .models import UserProfile
from .serializers import UserInfoSerializer, UserSerializer
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """Health check endpoint"""
    return Response({
        'status': 'healthy',
        'message': 'NVH Backend API is running'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    """Get current user information"""
    try:
        user = request.user
        
        # Get or create user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'keycloak_id': getattr(request, 'keycloak_user_info', {}).get('sub', '')
            }
        )
        
        # Prepare response data
        response_data = {
            'user': UserSerializer(user).data,
            'profile': {
                'keycloak_id': profile.keycloak_id,
                'department': profile.department,
                'phone': profile.phone,
                'created_at': profile.created_at,
                'updated_at': profile.updated_at,
            },
            'keycloak_info': getattr(request, 'keycloak_user_info', {})
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        return Response(
            {'error': 'Failed to get user information'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """Get current user profile"""
    try:
        user = request.user
        profile, created = UserProfile.objects.get_or_create(user=user)
        
        data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'department': profile.department,
            'phone': profile.phone,
            'is_active': user.is_active,
            'date_joined': user.date_joined,
        }
        
        return Response(data)
        
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return Response(
            {'error': 'Failed to get user profile'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
