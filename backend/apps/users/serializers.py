from rest_framework import serializers
from django.contrib.auth.models import User
from .models import UserProfile


class UserSerializer(serializers.ModelSerializer):
    """User serializer"""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_active', 'date_joined']
        read_only_fields = ['id', 'date_joined']


class UserProfileSerializer(serializers.ModelSerializer):
    """User profile serializer"""
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = UserProfile
        fields = ['user', 'keycloak_id', 'department', 'phone', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']


class UserInfoSerializer(serializers.Serializer):
    """Serializer for user info response"""
    user = UserSerializer()
    profile = UserProfileSerializer()
    keycloak_info = serializers.DictField(read_only=True)
