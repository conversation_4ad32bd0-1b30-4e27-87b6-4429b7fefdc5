"""
独立的Keycloak调试工具
不依赖中间件，直接测试Token验证
"""
import requests
import json
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

class KeycloakDebugger:
    def __init__(self):
        self.config = settings.KEYCLOAK_CONFIG
        self.realm = self.config['REALM']
        self.auth_server_url = self.config['AUTH_SERVER_URL']
        self.client_id = self.config['CLIENT_ID']
        self.client_secret = self.config['CLIENT_SECRET']
        
    def get_well_known_config(self):
        """获取Keycloak的well-known配置"""
        try:
            well_known_url = f"{self.auth_server_url}realms/{self.realm}/.well-known/openid-configuration"
            logger.info(f"Fetching well-known config from: {well_known_url}")
            
            response = requests.get(well_known_url, timeout=10)
            logger.info(f"Well-known config response status: {response.status_code}")
            
            if response.status_code == 200:
                config = response.json()
                return {
                    'success': True,
                    'config': config,
                    'endpoints': {
                        'token_endpoint': config.get('token_endpoint'),
                        'introspection_endpoint': config.get('introspection_endpoint'),
                        'userinfo_endpoint': config.get('userinfo_endpoint'),
                    }
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'url': well_known_url
                }
                
        except Exception as e:
            logger.error(f"Failed to get well-known config: {e}")
            return {
                'success': False,
                'error': str(e),
                'url': well_known_url
            }
    
    def test_token_introspection(self, token):
        """测试Token内省"""
        well_known = self.get_well_known_config()
        if not well_known['success']:
            return {
                'success': False,
                'error': 'Failed to get Keycloak configuration',
                'details': well_known
            }
        
        introspection_endpoint = well_known['endpoints']['introspection_endpoint']
        if not introspection_endpoint:
            return {
                'success': False,
                'error': 'Introspection endpoint not found in well-known config'
            }
        
        # 测试使用backend客户端
        backend_result = self._introspect_with_client(
            token, introspection_endpoint, self.client_id, self.client_secret
        )
        
        # 测试使用front客户端（public客户端）
        front_result = self._introspect_with_client(
            token, introspection_endpoint, 'front', None
        )
        
        return {
            'introspection_endpoint': introspection_endpoint,
            'backend_client_result': backend_result,
            'front_client_result': front_result,
            'recommendation': self._get_recommendation(backend_result, front_result)
        }
    
    def _introspect_with_client(self, token, endpoint, client_id, client_secret):
        """使用指定客户端进行Token内省"""
        try:
            data = {
                'token': token,
                'client_id': client_id
            }
            
            if client_secret:
                data['client_secret'] = client_secret
            
            logger.info(f"Introspecting with client_id: {client_id}")
            
            response = requests.post(
                endpoint,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            logger.info(f"Introspection response status: {response.status_code}")
            logger.info(f"Introspection response: {response.text}")
            
            if response.status_code == 200:
                token_info = response.json()
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'active': token_info.get('active', False),
                    'token_info': token_info,
                    'client_id_used': client_id,
                    'client_secret_used': bool(client_secret)
                }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': response.text,
                    'client_id_used': client_id,
                    'client_secret_used': bool(client_secret)
                }
                
        except Exception as e:
            logger.error(f"Introspection failed with client {client_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'client_id_used': client_id,
                'client_secret_used': bool(client_secret)
            }
    
    def test_userinfo_endpoint(self, token):
        """测试用户信息端点"""
        well_known = self.get_well_known_config()
        if not well_known['success']:
            return {
                'success': False,
                'error': 'Failed to get Keycloak configuration'
            }
        
        userinfo_endpoint = well_known['endpoints']['userinfo_endpoint']
        if not userinfo_endpoint:
            return {
                'success': False,
                'error': 'Userinfo endpoint not found in well-known config'
            }
        
        try:
            headers = {'Authorization': f'Bearer {token}'}
            response = requests.get(userinfo_endpoint, headers=headers, timeout=10)
            
            logger.info(f"Userinfo response status: {response.status_code}")
            logger.info(f"Userinfo response: {response.text}")
            
            if response.status_code == 200:
                user_info = response.json()
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'user_info': user_info,
                    'endpoint': userinfo_endpoint
                }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': response.text,
                    'endpoint': userinfo_endpoint
                }
                
        except Exception as e:
            logger.error(f"Userinfo request failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'endpoint': userinfo_endpoint
            }
    
    def _get_recommendation(self, backend_result, front_result):
        """根据测试结果提供建议"""
        recommendations = []
        
        if backend_result['success'] and backend_result.get('active'):
            recommendations.append("✅ Backend客户端验证成功，当前配置正确")
        elif front_result['success'] and front_result.get('active'):
            recommendations.append("⚠️ Front客户端验证成功，但Backend客户端失败")
            recommendations.append("建议：修改Django配置使用front客户端，或检查backend客户端配置")
        else:
            recommendations.append("❌ 两个客户端都验证失败")
            recommendations.append("建议：检查Token是否有效，或Keycloak服务器配置")
        
        return recommendations
    
    def full_debug(self, token):
        """完整的调试流程"""
        return {
            'keycloak_config': {
                'realm': self.realm,
                'auth_server_url': self.auth_server_url,
                'client_id': self.client_id,
                'client_secret_configured': bool(self.client_secret)
            },
            'token_info': {
                'length': len(token),
                'preview': token[:50] + '...' if len(token) > 50 else token
            },
            'well_known_config': self.get_well_known_config(),
            'token_introspection': self.test_token_introspection(token),
            'userinfo_test': self.test_userinfo_endpoint(token)
        }
