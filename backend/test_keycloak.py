#!/usr/bin/env python
"""
Keycloak后端集成测试脚本
用于测试Django后端的Keycloak认证功能
"""
import os
import sys
import requests
import json
from urllib.parse import urljoin

# 添加Django项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nvh_backend.settings')

import django
django.setup()

from utils.keycloak_utils import keycloak_utils

class KeycloakBackendTester:
    def __init__(self):
        self.base_url = 'http://localhost:8000'
        self.test_token = None
        
    def print_section(self, title):
        print(f"\n{'='*50}")
        print(f"{title}")
        print(f"{'='*50}")
    
    def test_server_connection(self):
        """测试Django服务器连接"""
        self.print_section("1. 测试Django服务器连接")
        
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            print(f"✅ 服务器连接成功")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
            return True
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
            return False
    
    def test_health_endpoint(self):
        """测试健康检查端点"""
        self.print_section("2. 测试健康检查端点")
        
        try:
            response = requests.get(f"{self.base_url}/api/auth/health/", timeout=10)
            print(f"✅ 健康检查成功")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
            return True
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False
    
    def test_keycloak_utils(self):
        """测试Keycloak工具类"""
        self.print_section("3. 测试Keycloak工具类")
        
        try:
            # 测试获取端点
            endpoints = keycloak_utils._get_endpoints()
            if endpoints:
                print(f"✅ Keycloak端点获取成功")
                print(f"Token端点: {endpoints.get('token_endpoint', 'N/A')}")
                print(f"用户信息端点: {endpoints.get('userinfo_endpoint', 'N/A')}")
                print(f"内省端点: {endpoints.get('introspection_endpoint', 'N/A')}")
                return True
            else:
                print(f"❌ Keycloak端点获取失败")
                return False
        except Exception as e:
            print(f"❌ Keycloak工具类测试失败: {e}")
            return False
    
    def get_test_token(self):
        """获取测试token（需要手动提供）"""
        self.print_section("4. 获取测试Token")
        
        print("请按以下步骤获取测试token:")
        print("1. 打开浏览器访问: http://localhost:5173/auth-test")
        print("2. 完成Keycloak登录")
        print("3. 在认证测试页面查看Token信息")
        print("4. 复制Token值")
        print()
        
        token = input("请输入Token (或按Enter跳过): ").strip()
        if token:
            self.test_token = token
            print(f"✅ Token已设置 (长度: {len(token)})")
            return True
        else:
            print("⚠️  跳过Token测试")
            return False
    
    def test_middleware_without_token(self):
        """测试中间件（无Token）"""
        self.print_section("5. 测试中间件（无Token）")
        
        try:
            response = requests.get(f"{self.base_url}/api/auth/middleware-test/", timeout=10)
            print(f"✅ 中间件测试成功（无Token）")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return True
        except Exception as e:
            print(f"❌ 中间件测试失败: {e}")
            return False
    
    def test_middleware_with_token(self):
        """测试中间件（有Token）"""
        if not self.test_token:
            print("⚠️  跳过Token中间件测试（无Token）")
            return False
            
        self.print_section("6. 测试中间件（有Token）")
        
        try:
            headers = {'Authorization': f'Bearer {self.test_token}'}
            response = requests.get(
                f"{self.base_url}/api/auth/middleware-test/", 
                headers=headers, 
                timeout=10
            )
            print(f"✅ 中间件测试成功（有Token）")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return True
        except Exception as e:
            print(f"❌ 中间件测试失败: {e}")
            return False
    
    def test_protected_endpoint(self):
        """测试受保护的端点"""
        if not self.test_token:
            print("⚠️  跳过受保护端点测试（无Token）")
            return False
            
        self.print_section("7. 测试受保护端点")
        
        try:
            headers = {'Authorization': f'Bearer {self.test_token}'}
            response = requests.get(
                f"{self.base_url}/api/users/keycloak-test/", 
                headers=headers, 
                timeout=10
            )
            print(f"✅ 受保护端点测试成功")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return True
        except Exception as e:
            print(f"❌ 受保护端点测试失败: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"错误响应: {e.response.text}")
            return False
    
    def test_user_info_endpoint(self):
        """测试用户信息端点"""
        if not self.test_token:
            print("⚠️  跳过用户信息端点测试（无Token）")
            return False
            
        self.print_section("8. 测试用户信息端点")
        
        try:
            headers = {'Authorization': f'Bearer {self.test_token}'}
            response = requests.get(
                f"{self.base_url}/api/users/info/", 
                headers=headers, 
                timeout=10
            )
            print(f"✅ 用户信息端点测试成功")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return True
        except Exception as e:
            print(f"❌ 用户信息端点测试失败: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"错误响应: {e.response.text}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("Keycloak后端集成测试开始")
        print(f"Django服务器: {self.base_url}")
        
        results = []
        results.append(self.test_server_connection())
        results.append(self.test_health_endpoint())
        results.append(self.test_keycloak_utils())
        results.append(self.get_test_token())
        results.append(self.test_middleware_without_token())
        results.append(self.test_middleware_with_token())
        results.append(self.test_protected_endpoint())
        results.append(self.test_user_info_endpoint())
        
        # 统计结果
        self.print_section("测试结果汇总")
        passed = sum(1 for r in results if r)
        total = len(results)
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("🎉 所有测试通过！Keycloak后端集成正常工作")
        else:
            print("⚠️  部分测试失败，请检查配置")

def main():
    tester = KeycloakBackendTester()
    tester.run_all_tests()

if __name__ == '__main__':
    main()
