"""
Keycloak authentication middleware
"""
import logging
from django.http import JsonResponse
from django.contrib.auth.models import AnonymousUser
from utils.keycloak_utils import keycloak_utils

logger = logging.getLogger(__name__)


class KeycloakMiddleware:
    """Middleware to handle Keycloak authentication"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        # Paths that don't require authentication
        self.exempt_paths = [
            '/admin/',
            '/api/auth/health/',
        ]
    
    def __call__(self, request):
        # Check if path is exempt from authentication
        if any(request.path.startswith(path) for path in self.exempt_paths):
            return self.get_response(request)
        
        # Extract token from Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            # 对于不需要认证的路径，设置匿名用户并继续
            request.user = AnonymousUser()
            return self.get_response(request)
        
        token = auth_header.split(' ')[1]
        
        # Validate token with Keycloak
        token_info = keycloak_utils.introspect_token(token)
        if not token_info:
            return JsonResponse(
                {'error': 'Invalid or expired token'}, 
                status=401
            )
        
        # Get user info from Keycloak
        user_info = keycloak_utils.get_user_info(token)
        if not user_info:
            return JsonResponse(
                {'error': 'Failed to get user information'}, 
                status=401
            )
        
        # Get or create Django user
        user = keycloak_utils.get_or_create_user(user_info)
        if not user:
            return JsonResponse(
                {'error': 'Failed to create user'}, 
                status=500
            )
        
        # Set user in request
        request.user = user
        request.keycloak_token_info = token_info
        request.keycloak_user_info = user_info
        
        response = self.get_response(request)
        return response
