"""
URL configuration for nvh_backend project.
"""
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse

def api_root(request):
    """API根路径，返回API信息"""
    return JsonResponse({
        'message': 'NVH数据管理系统 API',
        'version': '1.0.0',
        'endpoints': {
            'admin': '/admin/',
            'health': '/api/auth/health/',
            'user_info': '/api/users/info/',
            'user_profile': '/api/users/profile/',
        },
        'status': 'running'
    })

urlpatterns = [
    path('', api_root, name='api_root'),
    path('admin/', admin.site.urls),
    path('api/users/', include('apps.users.urls')),
    path('api/auth/', include('apps.users.urls')),
]
