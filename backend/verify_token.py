#!/usr/bin/env python
"""
JWT Token验证工具
用于验证和解析JWT Token
"""
import json
import base64
from datetime import datetime
import sys

def decode_jwt_payload(token):
    """解码JWT Token的payload部分"""
    try:
        # JWT格式: header.payload.signature
        parts = token.split('.')
        if len(parts) != 3:
            return None, "Invalid JWT format"
        
        # 解码payload (第二部分)
        payload = parts[1]
        
        # 添加padding如果需要
        padding = 4 - len(payload) % 4
        if padding != 4:
            payload += '=' * padding
        
        # Base64解码
        decoded_bytes = base64.urlsafe_b64decode(payload)
        payload_json = json.loads(decoded_bytes.decode('utf-8'))
        
        return payload_json, None
    except Exception as e:
        return None, str(e)

def format_timestamp(timestamp):
    """格式化Unix时间戳"""
    try:
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
    except:
        return "Invalid timestamp"

def verify_token(token):
    """验证Token信息"""
    print("=" * 60)
    print("JWT Token 验证工具")
    print("=" * 60)
    
    payload, error = decode_jwt_payload(token)
    if error:
        print(f"❌ Token解析失败: {error}")
        return False
    
    print("✅ Token解析成功")
    print("\n📋 Token信息:")
    print("-" * 40)
    
    # 基本信息
    print(f"发行者 (iss): {payload.get('iss', 'N/A')}")
    print(f"主题 (sub): {payload.get('sub', 'N/A')}")
    print(f"受众 (aud): {payload.get('aud', 'N/A')}")
    print(f"客户端 (azp): {payload.get('azp', 'N/A')}")
    
    # 时间信息
    iat = payload.get('iat')
    exp = payload.get('exp')
    auth_time = payload.get('auth_time')
    
    print(f"\n⏰ 时间信息:")
    print(f"签发时间 (iat): {format_timestamp(iat) if iat else 'N/A'}")
    print(f"过期时间 (exp): {format_timestamp(exp) if exp else 'N/A'}")
    print(f"认证时间 (auth_time): {format_timestamp(auth_time) if auth_time else 'N/A'}")
    
    # 检查是否过期
    if exp:
        current_time = datetime.now().timestamp()
        if current_time > exp:
            print("❌ Token已过期")
            return False
        else:
            remaining_minutes = int((exp - current_time) / 60)
            print(f"✅ Token有效，剩余时间: {remaining_minutes} 分钟")
    
    # 用户信息
    print(f"\n👤 用户信息:")
    print(f"用户名: {payload.get('preferred_username', 'N/A')}")
    print(f"邮箱: {payload.get('email', 'N/A')}")
    print(f"邮箱验证: {payload.get('email_verified', 'N/A')}")
    print(f"姓名: {payload.get('given_name', '')} {payload.get('family_name', '')}")
    
    # 权限信息
    realm_access = payload.get('realm_access', {})
    resource_access = payload.get('resource_access', {})
    
    print(f"\n🔐 权限信息:")
    print(f"Realm角色: {realm_access.get('roles', [])}")
    print(f"资源访问: {list(resource_access.keys())}")
    
    # 会话信息
    print(f"\n🔗 会话信息:")
    print(f"会话状态: {payload.get('session_state', 'N/A')}")
    print(f"会话ID: {payload.get('sid', 'N/A')}")
    print(f"作用域: {payload.get('scope', 'N/A')}")
    
    return True

def main():
    if len(sys.argv) > 1:
        token = sys.argv[1]
    else:
        # 使用你提供的token
        token = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJfSkxHZzJwNUZxTWsxaGhqZ0UzT2JMMjBRZ21rSVBPd2hJZmdiUDZ2VVdZIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cTi7jcad8BXvWK1ClGiY0fTx0ykMnkEX4vUIbBkc0eKMUQbg6gdR952Vl57ej_6LnUNJxzJ94hTs4IlRZfvE6DWpGlHHU_Pif9uEUM9HQdjjgsAtIDNkKdOWmQzn8Y456nbJllIUqoqbhWCvCi6e4dVIRJEm_pc177yucB8Htm99lsbJOA5Ypq5mOLZswOQ_oIsZqjXQT8fsDATyFKk6U6t7uJXwkMbAZjs9iFsTiuLlwayPwCp0x3owyhtaFHCbmS6iEUf7B-tp42Dqd0fzz4HOuynrOpaTZ6bO5mKwJJm86Yw4XCTFKHMyC0Wc51n1N6V6GgsFAfiJn8FT2mgKPw"
    
    verify_token(token)
    
    print(f"\n🔧 Apifox配置建议:")
    print("-" * 40)
    print("1. 在Authorization标签页选择 'Bearer Token'")
    print("2. 在Token字段粘贴JWT token（不需要Bearer前缀）")
    print("3. 或在Headers中添加:")
    print("   Key: Authorization")
    print("   Value: Bearer <your_token>")
    print("\n✅ 修正后重新测试API调用")

if __name__ == '__main__':
    main()
