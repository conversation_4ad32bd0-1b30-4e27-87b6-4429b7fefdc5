"""
Keycloak utility functions
"""
import requests
import json
from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


class KeycloakUtils:
    """Keycloak utility class for token validation and user info retrieval"""
    
    def __init__(self):
        self.config = settings.KEYCLOAK_CONFIG
        self.realm = self.config['REALM']
        self.auth_server_url = self.config['AUTH_SERVER_URL']
        self.client_id = self.config['CLIENT_ID']
        self.client_secret = self.config['CLIENT_SECRET']
        
        # Cache endpoints for 1 hour
        self.endpoints = self._get_endpoints()
    
    def _get_endpoints(self):
        """Get Keycloak endpoints from well-known configuration"""
        cache_key = f"keycloak_endpoints_{self.realm}"
        endpoints = cache.get(cache_key)
        
        if not endpoints:
            try:
                well_known_url = f"{self.auth_server_url}realms/{self.realm}/.well-known/openid-configuration"
                response = requests.get(well_known_url, timeout=10)
                response.raise_for_status()
                endpoints = response.json()
                cache.set(cache_key, endpoints, 3600)  # Cache for 1 hour
            except Exception as e:
                logger.error(f"Failed to get Keycloak endpoints: {e}")
                return None
        
        return endpoints
    
    def introspect_token(self, token):
        """Introspect token to validate and get token info"""
        if not self.endpoints:
            return None
            
        introspection_endpoint = self.endpoints.get('introspection_endpoint')
        if not introspection_endpoint:
            return None
        
        try:
            data = {
                'token': token,
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }
            
            response = requests.post(
                introspection_endpoint,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            response.raise_for_status()
            
            token_info = response.json()
            return token_info if token_info.get('active') else None
            
        except Exception as e:
            logger.error(f"Token introspection failed: {e}")
            return None
    
    def get_user_info(self, token):
        """Get user information from Keycloak"""
        if not self.endpoints:
            return None
            
        userinfo_endpoint = self.endpoints.get('userinfo_endpoint')
        if not userinfo_endpoint:
            return None
        
        try:
            headers = {'Authorization': f'Bearer {token}'}
            response = requests.get(userinfo_endpoint, headers=headers, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None
    
    def get_or_create_user(self, user_info):
        """Get or create Django user from Keycloak user info"""
        if not user_info:
            return None
        
        username = user_info.get('preferred_username') or user_info.get('sub')
        email = user_info.get('email', '')
        first_name = user_info.get('given_name', '')
        last_name = user_info.get('family_name', '')
        
        if not username:
            return None
        
        try:
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'is_active': True,
                }
            )
            
            # Update user info if not created
            if not created:
                user.email = email
                user.first_name = first_name
                user.last_name = last_name
                user.save()
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to get or create user: {e}")
            return None


# Global instance
keycloak_utils = KeycloakUtils()
