"""
Keycloak utility functions
"""
import requests
import json
from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


class KeycloakUtils:
    """Keycloak utility class for token validation and user info retrieval"""
    
    def __init__(self):
        self.config = settings.KEYCLOAK_CONFIG
        self.realm = self.config['REALM']
        self.auth_server_url = self.config['AUTH_SERVER_URL']
        self.client_id = self.config['CLIENT_ID']
        self.client_secret = self.config['CLIENT_SECRET']
        
        # Cache endpoints for 1 hour
        self.endpoints = self._get_endpoints()
    
    def _get_endpoints(self):
        """Get Keycloak endpoints from well-known configuration"""
        cache_key = f"keycloak_endpoints_{self.realm}"
        endpoints = cache.get(cache_key)
        
        if not endpoints:
            try:
                well_known_url = f"{self.auth_server_url}realms/{self.realm}/.well-known/openid-configuration"
                response = requests.get(well_known_url, timeout=10)
                response.raise_for_status()
                endpoints = response.json()
                cache.set(cache_key, endpoints, 3600)  # Cache for 1 hour
            except Exception as e:
                logger.error(f"Failed to get Keycloak endpoints: {e}")
                return None
        
        return endpoints
    
    def introspect_token(self, token):
        """Introspect token to validate and get token info"""
        if not self.endpoints:
            logger.error("Keycloak endpoints not available")
            return None

        introspection_endpoint = self.endpoints.get('introspection_endpoint')
        if not introspection_endpoint:
            logger.error("Introspection endpoint not found")
            return None

        try:
            # 尝试使用backend客户端进行内省
            data = {
                'token': token,
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }

            logger.info(f"Introspecting token at: {introspection_endpoint}")
            logger.info(f"Using client_id: {self.client_id}")

            response = requests.post(
                introspection_endpoint,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )

            logger.info(f"Introspection response status: {response.status_code}")
            logger.info(f"Introspection response: {response.text}")

            if response.status_code == 200:
                token_info = response.json()
                is_active = token_info.get('active', False)
                logger.info(f"Token active status: {is_active}")

                if is_active:
                    return token_info
                else:
                    logger.warning("Token is not active according to introspection")
                    return None
            else:
                logger.error(f"Introspection failed with status {response.status_code}")
                # 如果backend客户端内省失败，尝试使用front客户端
                return self._try_front_client_introspection(token, introspection_endpoint)

        except Exception as e:
            logger.error(f"Token introspection failed: {e}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"Response content: {e.response.text}")
            # 尝试使用front客户端
            return self._try_front_client_introspection(token, introspection_endpoint)

    def _try_front_client_introspection(self, token, introspection_endpoint):
        """尝试使用front客户端进行token内省（public客户端不需要secret）"""
        try:
            logger.info("Trying introspection with front client (public)")
            data = {
                'token': token,
                'client_id': 'front'  # 前端客户端ID
            }

            response = requests.post(
                introspection_endpoint,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )

            logger.info(f"Front client introspection response status: {response.status_code}")
            logger.info(f"Front client introspection response: {response.text}")

            if response.status_code == 200:
                token_info = response.json()
                is_active = token_info.get('active', False)
                logger.info(f"Front client token active status: {is_active}")
                return token_info if is_active else None
            else:
                logger.error(f"Front client introspection also failed with status {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Front client introspection failed: {e}")
            return None
    
    def get_user_info(self, token):
        """Get user information from Keycloak"""
        if not self.endpoints:
            return None
            
        userinfo_endpoint = self.endpoints.get('userinfo_endpoint')
        if not userinfo_endpoint:
            return None
        
        try:
            headers = {'Authorization': f'Bearer {token}'}
            response = requests.get(userinfo_endpoint, headers=headers, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None
    
    def get_or_create_user(self, user_info):
        """Get or create Django user from Keycloak user info"""
        if not user_info:
            return None
        
        username = user_info.get('preferred_username') or user_info.get('sub')
        email = user_info.get('email', '')
        first_name = user_info.get('given_name', '')
        last_name = user_info.get('family_name', '')
        
        if not username:
            return None
        
        try:
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'is_active': True,
                }
            )
            
            # Update user info if not created
            if not created:
                user.email = email
                user.first_name = first_name
                user.last_name = last_name
                user.save()
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to get or create user: {e}")
            return None


# Global instance
keycloak_utils = KeycloakUtils()
