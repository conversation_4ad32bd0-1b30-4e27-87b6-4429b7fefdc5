# Keycloak认证故障排除指南

## 常见问题及解决方案

### 1. CSP违规和iframe超时错误

**错误信息**:
```
Refused to frame 'https://account-test.sgmw.com.cn/' because an ancestor violates the following Content Security Policy directive: "frame-ancestors ..."
Keycloak initialization failed {error: 'Timeout when waiting for 3rd party check iframe message.'}
```

**原因**: 
Keycloak服务器的内容安全策略(CSP)不允许从`localhost:5173`进行iframe嵌入。

**解决方案**:
项目已实现自动解决方案：
- 开发环境使用 `keycloak-dev.js` 配置
- 禁用iframe检查：`checkLoginIframe: false`
- 使用标准授权码流程
- 启用PKCE安全增强

### 2. 认证测试

访问认证测试页面来诊断问题：
```
http://localhost:5173/auth-test
```

该页面提供：
- 认证状态检查
- Token信息显示
- 手动登录/登出测试
- API调用测试

### 3. 开发环境配置

**环境变量配置** (`.env.development`):
```env
VITE_KEYCLOAK_URL=https://account-test.sgmw.com.cn/auth/
VITE_KEYCLOAK_REALM=demo
VITE_KEYCLOAK_CLIENT_ID=front
```

**Keycloak初始化参数**:
```javascript
{
  onLoad: 'login-required',
  checkLoginIframe: false,           // 禁用iframe检查
  checkLoginIframeInterval: 0,       // 禁用定期检查
  enableLogging: true,               // 启用调试日志
  flow: 'standard',                  // 使用标准流程
  responseMode: 'query',             // 使用query模式
  silentCheckSsoRedirectUri: null,   // 禁用静默检查
  messageReceiveTimeout: 30000,      // 增加超时时间
  pkceMethod: 'S256'                 // 启用PKCE
}
```

### 4. 网络问题诊断

**检查Keycloak服务器连接**:
```bash
# 测试服务器连通性
curl -I https://account-test.sgmw.com.cn/auth/

# 获取realm配置
curl https://account-test.sgmw.com.cn/auth/realms/demo/.well-known/openid-configuration
```

**检查客户端配置**:
确保Keycloak服务器中的客户端配置包含：
- Client ID: `front`
- Client Protocol: `openid-connect`
- Access Type: `public`
- Valid Redirect URIs: `http://localhost:5173/*`

### 5. 浏览器调试

**开启Keycloak调试日志**:
在浏览器控制台中设置：
```javascript
localStorage.setItem('keycloak-debug', 'true')
```

**检查网络请求**:
1. 打开浏览器开发者工具
2. 切换到Network标签
3. 刷新页面，查看Keycloak相关请求
4. 检查是否有失败的请求

### 6. 常见错误代码

**401 Unauthorized**:
- Token已过期或无效
- 客户端配置错误
- 用户权限不足

**403 Forbidden**:
- 用户没有访问权限
- 客户端密钥错误（仅适用于confidential客户端）

**CORS错误**:
- Keycloak服务器CORS配置问题
- 客户端域名未在允许列表中

### 7. 生产环境注意事项

**安全配置**:
- 使用HTTPS
- 配置正确的重定向URI
- 启用PKCE
- 设置合适的token过期时间

**性能优化**:
- 启用token缓存
- 合理设置刷新间隔
- 使用CDN加速Keycloak JS库

### 8. 联系支持

如果问题仍然存在，请提供以下信息：
- 浏览器控制台错误日志
- 网络请求详情
- Keycloak服务器版本
- 客户端配置截图
- 重现步骤

## 测试账号

**测试环境账号**:
- 用户名: `test`
- 密码: `B5FDs0PcyuTipj^！`
- Realm: `demo`
- 登录地址: `https://account-test.sgmw.com.cn/auth/realms/demo/account/`
